#!/usr/bin/env node

/**
 * Advanced Canvas MCP Server Examples
 * 
 * This file demonstrates advanced graphics operations
 * including gradients, complex paths, and image manipulation.
 */

// Advanced graphics examples
const advancedExamples = {
  // Create a logo with gradients and effects
  createLogo: [
    {
      tool: "create_canvas",
      arguments: {
        width: 400,
        height: 200,
        backgroundColor: "#1a1a1a"
      }
    },
    {
      tool: "draw_circle",
      arguments: {
        x: 100,
        y: 100,
        radius: 60,
        fillColor: "#ff6b6b",
        strokeColor: "#ff5252",
        strokeWidth: 3
      }
    },
    {
      tool: "draw_rectangle",
      arguments: {
        x: 180,
        y: 60,
        width: 80,
        height: 80,
        fillColor: "#4ecdc4",
        strokeColor: "#26a69a",
        strokeWidth: 3
      }
    },
    {
      tool: "draw_text",
      arguments: {
        text: "BRAND",
        x: 300,
        y: 110,
        fontSize: 36,
        fontFamily: "Arial Black",
        fillColor: "#ffffff",
        textAlign: "left",
        textBaseline: "middle"
      }
    }
  ],

  // Create a complex geometric pattern
  geometricPattern: [
    {
      tool: "create_canvas",
      arguments: {
        width: 600,
        height: 600,
        backgroundColor: "#f8f9fa"
      }
    },
    // Draw multiple overlapping circles
    {
      tool: "draw_circle",
      arguments: {
        x: 200,
        y: 200,
        radius: 100,
        fillColor: "rgba(255, 107, 107, 0.6)",
        strokeColor: "#ff6b6b",
        strokeWidth: 2
      }
    },
    {
      tool: "draw_circle",
      arguments: {
        x: 400,
        y: 200,
        radius: 100,
        fillColor: "rgba(116, 185, 255, 0.6)",
        strokeColor: "#74b9ff",
        strokeWidth: 2
      }
    },
    {
      tool: "draw_circle",
      arguments: {
        x: 300,
        y: 350,
        radius: 100,
        fillColor: "rgba(0, 184, 148, 0.6)",
        strokeColor: "#00b894",
        strokeWidth: 2
      }
    },
    // Add geometric shapes
    {
      tool: "draw_path",
      arguments: {
        points: [
          { x: 300, y: 100 },
          { x: 450, y: 250 },
          { x: 300, y: 400 },
          { x: 150, y: 250 }
        ],
        strokeColor: "#2d3436",
        strokeWidth: 4,
        fillColor: "rgba(45, 52, 54, 0.1)",
        closed: true
      }
    }
  ],

  // Create an infographic element
  infographicElement: [
    {
      tool: "create_canvas",
      arguments: {
        width: 800,
        height: 300,
        backgroundColor: "#ffffff"
      }
    },
    // Progress bars
    {
      tool: "draw_rectangle",
      arguments: {
        x: 50,
        y: 50,
        width: 300,
        height: 20,
        fillColor: "#e9ecef",
        strokeColor: "#dee2e6",
        strokeWidth: 1
      }
    },
    {
      tool: "draw_rectangle",
      arguments: {
        x: 50,
        y: 50,
        width: 240,
        height: 20,
        fillColor: "#28a745"
      }
    },
    {
      tool: "draw_text",
      arguments: {
        text: "Progress: 80%",
        x: 370,
        y: 65,
        fontSize: 16,
        fontFamily: "Arial",
        fillColor: "#495057",
        textAlign: "left",
        textBaseline: "middle"
      }
    },
    // Statistics circles
    {
      tool: "draw_circle",
      arguments: {
        x: 150,
        y: 150,
        radius: 40,
        fillColor: "#007bff",
        strokeColor: "#0056b3",
        strokeWidth: 2
      }
    },
    {
      tool: "draw_text",
      arguments: {
        text: "42",
        x: 150,
        y: 145,
        fontSize: 24,
        fontFamily: "Arial Bold",
        fillColor: "#ffffff",
        textAlign: "center",
        textBaseline: "middle"
      }
    },
    {
      tool: "draw_text",
      arguments: {
        text: "Users",
        x: 150,
        y: 165,
        fontSize: 12,
        fontFamily: "Arial",
        fillColor: "#ffffff",
        textAlign: "center",
        textBaseline: "middle"
      }
    }
  ],

  // Create a data visualization
  dataVisualization: [
    {
      tool: "create_canvas",
      arguments: {
        width: 700,
        height: 500,
        backgroundColor: "#f8f9fa"
      }
    },
    // Title
    {
      tool: "draw_text",
      arguments: {
        text: "Monthly Revenue",
        x: 350,
        y: 40,
        fontSize: 28,
        fontFamily: "Arial",
        fillColor: "#212529",
        textAlign: "center"
      }
    },
    // Draw axes
    {
      tool: "draw_line",
      arguments: {
        startX: 80,
        startY: 80,
        endX: 80,
        endY: 420,
        strokeColor: "#6c757d",
        strokeWidth: 2
      }
    },
    {
      tool: "draw_line",
      arguments: {
        startX: 80,
        startY: 420,
        endX: 620,
        endY: 420,
        strokeColor: "#6c757d",
        strokeWidth: 2
      }
    },
    // Data points and lines
    {
      tool: "draw_path",
      arguments: {
        points: [
          { x: 120, y: 380 },
          { x: 180, y: 320 },
          { x: 240, y: 280 },
          { x: 300, y: 200 },
          { x: 360, y: 160 },
          { x: 420, y: 140 },
          { x: 480, y: 120 },
          { x: 540, y: 100 }
        ],
        strokeColor: "#007bff",
        strokeWidth: 3,
        closed: false
      }
    },
    // Data points
    {
      tool: "draw_circle",
      arguments: { x: 120, y: 380, radius: 4, fillColor: "#007bff" }
    },
    {
      tool: "draw_circle",
      arguments: { x: 180, y: 320, radius: 4, fillColor: "#007bff" }
    },
    {
      tool: "draw_circle",
      arguments: { x: 240, y: 280, radius: 4, fillColor: "#007bff" }
    },
    {
      tool: "draw_circle",
      arguments: { x: 300, y: 200, radius: 4, fillColor: "#007bff" }
    },
    {
      tool: "draw_circle",
      arguments: { x: 360, y: 160, radius: 4, fillColor: "#007bff" }
    },
    {
      tool: "draw_circle",
      arguments: { x: 420, y: 140, radius: 4, fillColor: "#007bff" }
    },
    {
      tool: "draw_circle",
      arguments: { x: 480, y: 120, radius: 4, fillColor: "#007bff" }
    },
    {
      tool: "draw_circle",
      arguments: { x: 540, y: 100, radius: 4, fillColor: "#007bff" }
    },
    // Labels
    {
      tool: "draw_text",
      arguments: {
        text: "Jan",
        x: 120,
        y: 440,
        fontSize: 12,
        textAlign: "center",
        fillColor: "#6c757d"
      }
    },
    {
      tool: "draw_text",
      arguments: {
        text: "Feb",
        x: 180,
        y: 440,
        fontSize: 12,
        textAlign: "center",
        fillColor: "#6c757d"
      }
    },
    {
      tool: "draw_text",
      arguments: {
        text: "Mar",
        x: 240,
        y: 440,
        fontSize: 12,
        textAlign: "center",
        fillColor: "#6c757d"
      }
    }
  ],

  // Apply artistic effects
  artisticEffects: [
    {
      tool: "apply_filter",
      arguments: {
        type: "blur",
        radius: 1
      }
    },
    {
      tool: "apply_filter",
      arguments: {
        type: "contrast",
        value: 1.1
      }
    },
    {
      tool: "apply_filter",
      arguments: {
        type: "saturate",
        value: 1.2
      }
    }
  ]
};

// Workflow for creating a complete dashboard element
const dashboardWorkflow = [
  ...advancedExamples.infographicElement,
  ...advancedExamples.artisticEffects,
  {
    tool: "export_canvas",
    arguments: {
      format: "png",
      quality: 0.95
    }
  }
];

console.log('Advanced Canvas Graphics Examples');
console.log('=================================');
console.log('');
console.log('Available examples:');
console.log('- Logo creation');
console.log('- Geometric patterns');
console.log('- Infographic elements');
console.log('- Data visualization');
console.log('- Artistic effects');

export { advancedExamples, dashboardWorkflow };
