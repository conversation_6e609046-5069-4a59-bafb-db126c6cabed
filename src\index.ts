#!/usr/bin/env node

import { Server } from '@modelcontextprotocol/sdk/server/index.js';
import { StdioServerTransport } from '@modelcontextprotocol/sdk/server/stdio.js';
import {
  CallToolRequestSchema,
  ListToolsRequestSchema,
  ErrorCode,
  McpError,
} from '@modelcontextprotocol/sdk/types.js';

import { CanvasManager } from './canvas-manager.js';
import { canvasTools } from './tools.js';
import {
  CanvasConfigSchema,
  RectangleSchema,
  CircleSchema,
  LineSchema,
  TextSchema,
  PathSchema,
  ImageSchema,
  FilterSchema,
} from './types.js';

class CanvasMCPServer {
  private server: Server;
  private canvasManager: CanvasManager | null = null;

  constructor() {
    this.server = new Server(
      {
        name: 'canvas-mcp-server',
        version: '1.0.0',
      },
      {
        capabilities: {
          tools: {},
        },
      }
    );

    this.setupToolHandlers();
    this.setupErrorHandling();
  }

  private setupErrorHandling(): void {
    this.server.onerror = (error) => {
      console.error('[MCP Error]', error);
    };

    process.on('SIGINT', async () => {
      await this.server.close();
      process.exit(0);
    });
  }

  private setupToolHandlers(): void {
    this.server.setRequestHandler(ListToolsRequestSchema, async () => ({
      tools: canvasTools,
    }));

    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'create_canvas':
            return await this.handleCreateCanvas(args);
          case 'draw_rectangle':
            return await this.handleDrawRectangle(args);
          case 'draw_circle':
            return await this.handleDrawCircle(args);
          case 'draw_line':
            return await this.handleDrawLine(args);
          case 'draw_text':
            return await this.handleDrawText(args);
          case 'draw_path':
            return await this.handleDrawPath(args);
          case 'draw_image':
            return await this.handleDrawImage(args);
          case 'apply_filter':
            return await this.handleApplyFilter(args);
          case 'clear_canvas':
            return await this.handleClearCanvas();
          case 'export_canvas':
            return await this.handleExportCanvas(args);
          case 'get_canvas_info':
            return await this.handleGetCanvasInfo();
          case 'resize_canvas':
            return await this.handleResizeCanvas(args);
          default:
            throw new McpError(
              ErrorCode.MethodNotFound,
              `Unknown tool: ${name}`
            );
        }
      } catch (error) {
        if (error instanceof McpError) {
          throw error;
        }
        throw new McpError(
          ErrorCode.InternalError,
          `Tool execution failed: ${error}`
        );
      }
    });
  }

  private ensureCanvas(): void {
    if (!this.canvasManager) {
      throw new McpError(
        ErrorCode.InvalidRequest,
        'No canvas created. Use create_canvas first.'
      );
    }
  }

  private async handleCreateCanvas(args: any) {
    try {
      const config = CanvasConfigSchema.parse(args);
      this.canvasManager = new CanvasManager(config);
      
      return {
        content: [
          {
            type: 'text',
            text: `Canvas created successfully with dimensions ${config.width}x${config.height}`,
          },
        ],
      };
    } catch (error) {
      throw new McpError(
        ErrorCode.InvalidParams,
        `Invalid canvas configuration: ${error}`
      );
    }
  }

  private async handleDrawRectangle(args: any) {
    this.ensureCanvas();
    try {
      const rectangle = RectangleSchema.parse(args);
      this.canvasManager!.drawRectangle(rectangle);
      
      return {
        content: [
          {
            type: 'text',
            text: `Rectangle drawn at (${rectangle.x}, ${rectangle.y}) with size ${rectangle.width}x${rectangle.height}`,
          },
        ],
      };
    } catch (error) {
      throw new McpError(
        ErrorCode.InvalidParams,
        `Invalid rectangle parameters: ${error}`
      );
    }
  }

  private async handleDrawCircle(args: any) {
    this.ensureCanvas();
    try {
      const circle = CircleSchema.parse(args);
      this.canvasManager!.drawCircle(circle);
      
      return {
        content: [
          {
            type: 'text',
            text: `Circle drawn at (${circle.x}, ${circle.y}) with radius ${circle.radius}`,
          },
        ],
      };
    } catch (error) {
      throw new McpError(
        ErrorCode.InvalidParams,
        `Invalid circle parameters: ${error}`
      );
    }
  }

  private async handleDrawLine(args: any) {
    this.ensureCanvas();
    try {
      const line = LineSchema.parse(args);
      this.canvasManager!.drawLine(line);
      
      return {
        content: [
          {
            type: 'text',
            text: `Line drawn from (${line.startX}, ${line.startY}) to (${line.endX}, ${line.endY})`,
          },
        ],
      };
    } catch (error) {
      throw new McpError(
        ErrorCode.InvalidParams,
        `Invalid line parameters: ${error}`
      );
    }
  }

  private async handleDrawText(args: any) {
    this.ensureCanvas();
    try {
      const text = TextSchema.parse(args);
      this.canvasManager!.drawText(text);
      
      return {
        content: [
          {
            type: 'text',
            text: `Text "${text.text}" drawn at (${text.x}, ${text.y})`,
          },
        ],
      };
    } catch (error) {
      throw new McpError(
        ErrorCode.InvalidParams,
        `Invalid text parameters: ${error}`
      );
    }
  }

  private async handleDrawPath(args: any) {
    this.ensureCanvas();
    try {
      const path = PathSchema.parse(args);
      this.canvasManager!.drawPath(path);
      
      return {
        content: [
          {
            type: 'text',
            text: `Path drawn with ${path.points.length} points`,
          },
        ],
      };
    } catch (error) {
      throw new McpError(
        ErrorCode.InvalidParams,
        `Invalid path parameters: ${error}`
      );
    }
  }

  private async handleDrawImage(args: any) {
    this.ensureCanvas();
    try {
      const image = ImageSchema.parse(args);
      await this.canvasManager!.drawImage(image);
      
      return {
        content: [
          {
            type: 'text',
            text: `Image drawn at (${image.x}, ${image.y})`,
          },
        ],
      };
    } catch (error) {
      throw new McpError(
        ErrorCode.InvalidParams,
        `Invalid image parameters: ${error}`
      );
    }
  }

  private async handleApplyFilter(args: any) {
    this.ensureCanvas();
    try {
      const filter = FilterSchema.parse(args);
      await this.canvasManager!.applyFilter(filter);
      
      return {
        content: [
          {
            type: 'text',
            text: `Filter "${filter.type}" applied to canvas`,
          },
        ],
      };
    } catch (error) {
      throw new McpError(
        ErrorCode.InvalidParams,
        `Invalid filter parameters: ${error}`
      );
    }
  }

  private async handleClearCanvas() {
    this.ensureCanvas();
    this.canvasManager!.clear();
    
    return {
      content: [
        {
          type: 'text',
          text: 'Canvas cleared successfully',
        },
      ],
    };
  }

  private async handleExportCanvas(args: any) {
    this.ensureCanvas();
    const result = await this.canvasManager!.export();
    
    if (!result.success) {
      throw new McpError(
        ErrorCode.InternalError,
        result.message || 'Export failed'
      );
    }
    
    return {
      content: [
        {
          type: 'text',
          text: `Canvas exported successfully as ${result.metadata?.format} (${result.metadata?.size} bytes)`,
        },
        {
          type: 'image',
          data: result.data!,
          mimeType: `image/${result.metadata?.format}`,
        },
      ],
    };
  }

  private async handleGetCanvasInfo() {
    this.ensureCanvas();
    const dimensions = this.canvasManager!.getDimensions();
    
    return {
      content: [
        {
          type: 'text',
          text: `Canvas dimensions: ${dimensions.width}x${dimensions.height}`,
        },
      ],
    };
  }

  private async handleResizeCanvas(args: any) {
    this.ensureCanvas();
    const { width, height } = args;
    
    if (typeof width !== 'number' || typeof height !== 'number') {
      throw new McpError(
        ErrorCode.InvalidParams,
        'Width and height must be numbers'
      );
    }
    
    this.canvasManager!.resize(width, height);
    
    return {
      content: [
        {
          type: 'text',
          text: `Canvas resized to ${width}x${height}`,
        },
      ],
    };
  }

  async run(): Promise<void> {
    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.error('Canvas MCP server running on stdio');
  }
}

const server = new CanvasMCPServer();
server.run().catch(console.error);
