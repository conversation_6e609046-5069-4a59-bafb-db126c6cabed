import { Tool } from '@modelcontextprotocol/sdk/types.js';

export const canvasTools: Tool[] = [
  {
    name: 'create_canvas',
    description: 'Create a new canvas with specified dimensions and configuration',
    inputSchema: {
      type: 'object',
      properties: {
        width: {
          type: 'number',
          description: 'Canvas width in pixels',
          minimum: 1,
          maximum: 4096,
          default: 800
        },
        height: {
          type: 'number',
          description: 'Canvas height in pixels',
          minimum: 1,
          maximum: 4096,
          default: 600
        },
        backgroundColor: {
          type: 'string',
          description: 'Background color (hex, rgb, rgba, hsl, hsla)',
          default: '#ffffff'
        },
        format: {
          type: 'string',
          enum: ['png', 'jpeg', 'webp', 'svg'],
          description: 'Output format',
          default: 'png'
        },
        quality: {
          type: 'number',
          description: 'Image quality (0-1)',
          minimum: 0,
          maximum: 1,
          default: 0.9
        }
      }
    }
  },
  {
    name: 'draw_rectangle',
    description: 'Draw a rectangle on the canvas',
    inputSchema: {
      type: 'object',
      properties: {
        x: { type: 'number', description: 'X coordinate' },
        y: { type: 'number', description: 'Y coordinate' },
        width: { type: 'number', description: 'Rectangle width', minimum: 0 },
        height: { type: 'number', description: 'Rectangle height', minimum: 0 },
        fillColor: { type: 'string', description: 'Fill color (optional)' },
        strokeColor: { type: 'string', description: 'Stroke color (optional)' },
        strokeWidth: { type: 'number', description: 'Stroke width', minimum: 0, default: 1 }
      },
      required: ['x', 'y', 'width', 'height']
    }
  },
  {
    name: 'draw_circle',
    description: 'Draw a circle on the canvas',
    inputSchema: {
      type: 'object',
      properties: {
        x: { type: 'number', description: 'Center X coordinate' },
        y: { type: 'number', description: 'Center Y coordinate' },
        radius: { type: 'number', description: 'Circle radius', minimum: 0 },
        fillColor: { type: 'string', description: 'Fill color (optional)' },
        strokeColor: { type: 'string', description: 'Stroke color (optional)' },
        strokeWidth: { type: 'number', description: 'Stroke width', minimum: 0, default: 1 }
      },
      required: ['x', 'y', 'radius']
    }
  },
  {
    name: 'draw_line',
    description: 'Draw a line on the canvas',
    inputSchema: {
      type: 'object',
      properties: {
        startX: { type: 'number', description: 'Start X coordinate' },
        startY: { type: 'number', description: 'Start Y coordinate' },
        endX: { type: 'number', description: 'End X coordinate' },
        endY: { type: 'number', description: 'End Y coordinate' },
        strokeColor: { type: 'string', description: 'Line color', default: '#000000' },
        strokeWidth: { type: 'number', description: 'Line width', minimum: 0, default: 1 },
        lineCap: { 
          type: 'string', 
          enum: ['butt', 'round', 'square'], 
          description: 'Line cap style',
          default: 'butt'
        }
      },
      required: ['startX', 'startY', 'endX', 'endY']
    }
  },
  {
    name: 'draw_text',
    description: 'Draw text on the canvas',
    inputSchema: {
      type: 'object',
      properties: {
        text: { type: 'string', description: 'Text to draw' },
        x: { type: 'number', description: 'X coordinate' },
        y: { type: 'number', description: 'Y coordinate' },
        fontSize: { type: 'number', description: 'Font size', minimum: 1, default: 16 },
        fontFamily: { type: 'string', description: 'Font family', default: 'Arial' },
        fillColor: { type: 'string', description: 'Text color', default: '#000000' },
        textAlign: {
          type: 'string',
          enum: ['left', 'center', 'right', 'start', 'end'],
          description: 'Text alignment',
          default: 'left'
        },
        textBaseline: {
          type: 'string',
          enum: ['top', 'hanging', 'middle', 'alphabetic', 'ideographic', 'bottom'],
          description: 'Text baseline',
          default: 'alphabetic'
        }
      },
      required: ['text', 'x', 'y']
    }
  },
  {
    name: 'draw_path',
    description: 'Draw a path with multiple points',
    inputSchema: {
      type: 'object',
      properties: {
        points: {
          type: 'array',
          items: {
            type: 'object',
            properties: {
              x: { type: 'number' },
              y: { type: 'number' }
            },
            required: ['x', 'y']
          },
          minItems: 2,
          description: 'Array of points'
        },
        strokeColor: { type: 'string', description: 'Stroke color', default: '#000000' },
        strokeWidth: { type: 'number', description: 'Stroke width', minimum: 0, default: 1 },
        fillColor: { type: 'string', description: 'Fill color (optional)' },
        closed: { type: 'boolean', description: 'Whether to close the path', default: false }
      },
      required: ['points']
    }
  },
  {
    name: 'draw_image',
    description: 'Draw an image on the canvas',
    inputSchema: {
      type: 'object',
      properties: {
        src: { type: 'string', description: 'Image source (base64 data URL or file path)' },
        x: { type: 'number', description: 'X coordinate', default: 0 },
        y: { type: 'number', description: 'Y coordinate', default: 0 },
        width: { type: 'number', description: 'Image width (optional)' },
        height: { type: 'number', description: 'Image height (optional)' },
        opacity: { type: 'number', description: 'Image opacity (0-1)', minimum: 0, maximum: 1, default: 1 }
      },
      required: ['src']
    }
  },
  {
    name: 'apply_filter',
    description: 'Apply a filter to the canvas',
    inputSchema: {
      type: 'object',
      properties: {
        type: {
          type: 'string',
          enum: ['blur', 'brightness', 'contrast', 'saturate'],
          description: 'Filter type'
        },
        radius: { type: 'number', description: 'Blur radius (for blur filter)', minimum: 0, maximum: 100 },
        value: { type: 'number', description: 'Filter value (for brightness, contrast, saturate)', minimum: 0, maximum: 2 }
      },
      required: ['type']
    }
  },
  {
    name: 'clear_canvas',
    description: 'Clear the canvas with background color',
    inputSchema: {
      type: 'object',
      properties: {}
    }
  },
  {
    name: 'export_canvas',
    description: 'Export the canvas as base64 encoded image',
    inputSchema: {
      type: 'object',
      properties: {
        format: {
          type: 'string',
          enum: ['png', 'jpeg', 'webp'],
          description: 'Export format',
          default: 'png'
        },
        quality: {
          type: 'number',
          description: 'Image quality (0-1)',
          minimum: 0,
          maximum: 1,
          default: 0.9
        }
      }
    }
  },
  {
    name: 'get_canvas_info',
    description: 'Get canvas dimensions and configuration',
    inputSchema: {
      type: 'object',
      properties: {}
    }
  },
  {
    name: 'resize_canvas',
    description: 'Resize the canvas',
    inputSchema: {
      type: 'object',
      properties: {
        width: { type: 'number', description: 'New width', minimum: 1, maximum: 4096 },
        height: { type: 'number', description: 'New height', minimum: 1, maximum: 4096 }
      },
      required: ['width', 'height']
    }
  }
];
