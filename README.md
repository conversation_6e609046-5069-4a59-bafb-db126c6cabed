# Canvas MCP Server

A Model Context Protocol (MCP) server that provides canvas drawing and graphics operations. This server allows AI assistants to create, manipulate, and export canvas-based graphics programmatically.

## Features

### Core Canvas Operations
- **Create Canvas**: Initialize canvas with custom dimensions and background
- **Clear Canvas**: Reset canvas to background color
- **Resize Canvas**: Change canvas dimensions while preserving content
- **Export Canvas**: Generate base64-encoded images in multiple formats

### Drawing Operations
- **Rectangles**: Draw filled or outlined rectangles
- **Circles**: Draw filled or outlined circles
- **Lines**: Draw lines with customizable width and cap styles
- **Text**: Render text with font customization and alignment
- **Paths**: Draw complex paths with multiple points
- **Images**: Load and draw images from base64 or file paths

### Advanced Features
- **Filters**: Apply blur, brightness, contrast, and saturation filters
- **Gradients**: Create linear and radial gradients
- **Composite Operations**: Control how new shapes interact with existing content
- **State Management**: Save and restore canvas states

## Installation

```bash
# Clone or create the canvas-mcp directory
cd canvas-mcp

# Install dependencies
npm install

# Build the project
npm run build

# Run in development mode
npm run dev
```

## Usage

### Basic Canvas Creation

```typescript
// Create a new canvas
{
  "tool": "create_canvas",
  "arguments": {
    "width": 800,
    "height": 600,
    "backgroundColor": "#ffffff",
    "format": "png"
  }
}
```

### Drawing Shapes

```typescript
// Draw a rectangle
{
  "tool": "draw_rectangle",
  "arguments": {
    "x": 100,
    "y": 100,
    "width": 200,
    "height": 150,
    "fillColor": "#ff0000",
    "strokeColor": "#000000",
    "strokeWidth": 2
  }
}

// Draw a circle
{
  "tool": "draw_circle",
  "arguments": {
    "x": 400,
    "y": 300,
    "radius": 50,
    "fillColor": "#00ff00"
  }
}
```

### Adding Text

```typescript
{
  "tool": "draw_text",
  "arguments": {
    "text": "Hello, Canvas!",
    "x": 100,
    "y": 50,
    "fontSize": 24,
    "fontFamily": "Arial",
    "fillColor": "#333333",
    "textAlign": "center"
  }
}
```

### Drawing Complex Paths

```typescript
{
  "tool": "draw_path",
  "arguments": {
    "points": [
      {"x": 100, "y": 100},
      {"x": 200, "y": 50},
      {"x": 300, "y": 100},
      {"x": 250, "y": 200}
    ],
    "strokeColor": "#0000ff",
    "strokeWidth": 3,
    "fillColor": "#ccccff",
    "closed": true
  }
}
```

### Applying Filters

```typescript
// Apply blur filter
{
  "tool": "apply_filter",
  "arguments": {
    "type": "blur",
    "radius": 5
  }
}

// Adjust brightness
{
  "tool": "apply_filter",
  "arguments": {
    "type": "brightness",
    "value": 1.2
  }
}
```

### Exporting Canvas

```typescript
{
  "tool": "export_canvas",
  "arguments": {
    "format": "png",
    "quality": 0.9
  }
}
```

## API Reference

### Tools

#### `create_canvas`
Initialize a new canvas with specified dimensions and configuration.

**Parameters:**
- `width` (number): Canvas width in pixels (1-4096)
- `height` (number): Canvas height in pixels (1-4096)
- `backgroundColor` (string): Background color (hex, rgb, rgba, hsl, hsla)
- `format` (string): Output format ('png', 'jpeg', 'webp', 'svg')
- `quality` (number): Image quality (0-1)

#### `draw_rectangle`
Draw a rectangle on the canvas.

**Parameters:**
- `x`, `y` (number): Position coordinates
- `width`, `height` (number): Rectangle dimensions
- `fillColor` (string, optional): Fill color
- `strokeColor` (string, optional): Stroke color
- `strokeWidth` (number): Stroke width

#### `draw_circle`
Draw a circle on the canvas.

**Parameters:**
- `x`, `y` (number): Center coordinates
- `radius` (number): Circle radius
- `fillColor` (string, optional): Fill color
- `strokeColor` (string, optional): Stroke color
- `strokeWidth` (number): Stroke width

#### `draw_line`
Draw a line on the canvas.

**Parameters:**
- `startX`, `startY` (number): Start coordinates
- `endX`, `endY` (number): End coordinates
- `strokeColor` (string): Line color
- `strokeWidth` (number): Line width
- `lineCap` (string): Line cap style ('butt', 'round', 'square')

#### `draw_text`
Draw text on the canvas.

**Parameters:**
- `text` (string): Text to draw
- `x`, `y` (number): Position coordinates
- `fontSize` (number): Font size
- `fontFamily` (string): Font family
- `fillColor` (string): Text color
- `textAlign` (string): Text alignment
- `textBaseline` (string): Text baseline

#### `draw_path`
Draw a path with multiple points.

**Parameters:**
- `points` (array): Array of {x, y} coordinates
- `strokeColor` (string): Stroke color
- `strokeWidth` (number): Stroke width
- `fillColor` (string, optional): Fill color
- `closed` (boolean): Whether to close the path

#### `draw_image`
Draw an image on the canvas.

**Parameters:**
- `src` (string): Image source (base64 data URL or file path)
- `x`, `y` (number): Position coordinates
- `width`, `height` (number, optional): Image dimensions
- `opacity` (number): Image opacity (0-1)

#### `apply_filter`
Apply a filter to the canvas.

**Parameters:**
- `type` (string): Filter type ('blur', 'brightness', 'contrast', 'saturate')
- `radius` (number): Blur radius (for blur filter)
- `value` (number): Filter value (for other filters)

#### `clear_canvas`
Clear the canvas with background color.

#### `export_canvas`
Export the canvas as base64 encoded image.

**Parameters:**
- `format` (string): Export format ('png', 'jpeg', 'webp')
- `quality` (number): Image quality (0-1)

#### `get_canvas_info`
Get canvas dimensions and configuration.

#### `resize_canvas`
Resize the canvas.

**Parameters:**
- `width`, `height` (number): New dimensions

## Dependencies

- `@modelcontextprotocol/sdk`: MCP SDK for server implementation
- `canvas`: Node.js canvas implementation for server-side rendering
- `sharp`: High-performance image processing for filters
- `zod`: Runtime type validation

## Development

```bash
# Install dependencies
npm install

# Run in development mode with auto-reload
npm run dev

# Build for production
npm run build

# Run tests
npm test

# Lint code
npm run lint

# Format code
npm run format
```

## License

MIT License - see LICENSE file for details.
