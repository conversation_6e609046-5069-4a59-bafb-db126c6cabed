#!/usr/bin/env node

/**
 * Basic Canvas MCP Server Usage Examples
 * 
 * This file demonstrates how to use the Canvas MCP server
 * for various drawing operations.
 */

// Example MCP tool calls that can be made to the Canvas server

const examples = {
  // 1. Create a new canvas
  createCanvas: {
    tool: "create_canvas",
    arguments: {
      width: 800,
      height: 600,
      backgroundColor: "#f0f0f0",
      format: "png",
      quality: 0.9
    }
  },

  // 2. Draw basic shapes
  drawRectangle: {
    tool: "draw_rectangle",
    arguments: {
      x: 100,
      y: 100,
      width: 200,
      height: 150,
      fillColor: "#ff6b6b",
      strokeColor: "#d63031",
      strokeWidth: 3
    }
  },

  drawCircle: {
    tool: "draw_circle",
    arguments: {
      x: 500,
      y: 200,
      radius: 75,
      fillColor: "#74b9ff",
      strokeColor: "#0984e3",
      strokeWidth: 2
    }
  },

  // 3. Draw lines and paths
  drawLine: {
    tool: "draw_line",
    arguments: {
      startX: 50,
      startY: 300,
      endX: 750,
      endY: 350,
      strokeColor: "#00b894",
      strokeWidth: 5,
      lineCap: "round"
    }
  },

  drawPath: {
    tool: "draw_path",
    arguments: {
      points: [
        { x: 300, y: 400 },
        { x: 350, y: 350 },
        { x: 400, y: 400 },
        { x: 450, y: 350 },
        { x: 500, y: 400 }
      ],
      strokeColor: "#e17055",
      strokeWidth: 4,
      fillColor: "#fab1a0",
      closed: false
    }
  },

  // 4. Add text
  drawText: {
    tool: "draw_text",
    arguments: {
      text: "Canvas MCP Server Demo",
      x: 400,
      y: 50,
      fontSize: 32,
      fontFamily: "Arial",
      fillColor: "#2d3436",
      textAlign: "center",
      textBaseline: "middle"
    }
  },

  // 5. Apply filters
  applyBlur: {
    tool: "apply_filter",
    arguments: {
      type: "blur",
      radius: 2
    }
  },

  adjustBrightness: {
    tool: "apply_filter",
    arguments: {
      type: "brightness",
      value: 1.1
    }
  },

  // 6. Export the final result
  exportCanvas: {
    tool: "export_canvas",
    arguments: {
      format: "png",
      quality: 0.95
    }
  },

  // 7. Get canvas information
  getCanvasInfo: {
    tool: "get_canvas_info",
    arguments: {}
  },

  // 8. Clear and resize operations
  clearCanvas: {
    tool: "clear_canvas",
    arguments: {}
  },

  resizeCanvas: {
    tool: "resize_canvas",
    arguments: {
      width: 1024,
      height: 768
    }
  }
};

// Example workflow: Create a simple diagram
const diagramWorkflow = [
  // 1. Create canvas
  examples.createCanvas,
  
  // 2. Add title
  examples.drawText,
  
  // 3. Draw some shapes
  examples.drawRectangle,
  examples.drawCircle,
  
  // 4. Connect with lines
  examples.drawLine,
  examples.drawPath,
  
  // 5. Apply subtle effects
  examples.adjustBrightness,
  
  // 6. Export result
  examples.exportCanvas
];

// Example: Creating a chart
const chartExample = {
  createCanvas: {
    tool: "create_canvas",
    arguments: {
      width: 600,
      height: 400,
      backgroundColor: "#ffffff"
    }
  },
  
  drawAxes: [
    {
      tool: "draw_line",
      arguments: {
        startX: 50,
        startY: 350,
        endX: 550,
        endY: 350,
        strokeColor: "#333333",
        strokeWidth: 2
      }
    },
    {
      tool: "draw_line",
      arguments: {
        startX: 50,
        startY: 50,
        endX: 50,
        endY: 350,
        strokeColor: "#333333",
        strokeWidth: 2
      }
    }
  ],
  
  drawBars: [
    {
      tool: "draw_rectangle",
      arguments: {
        x: 100,
        y: 250,
        width: 40,
        height: 100,
        fillColor: "#e74c3c"
      }
    },
    {
      tool: "draw_rectangle",
      arguments: {
        x: 200,
        y: 200,
        width: 40,
        height: 150,
        fillColor: "#3498db"
      }
    },
    {
      tool: "draw_rectangle",
      arguments: {
        x: 300,
        y: 150,
        width: 40,
        height: 200,
        fillColor: "#2ecc71"
      }
    }
  ],
  
  addLabels: [
    {
      tool: "draw_text",
      arguments: {
        text: "Sales Chart",
        x: 300,
        y: 30,
        fontSize: 24,
        fontFamily: "Arial",
        fillColor: "#2c3e50",
        textAlign: "center"
      }
    },
    {
      tool: "draw_text",
      arguments: {
        text: "Q1",
        x: 120,
        y: 370,
        fontSize: 14,
        textAlign: "center"
      }
    },
    {
      tool: "draw_text",
      arguments: {
        text: "Q2",
        x: 220,
        y: 370,
        fontSize: 14,
        textAlign: "center"
      }
    },
    {
      tool: "draw_text",
      arguments: {
        text: "Q3",
        x: 320,
        y: 370,
        fontSize: 14,
        textAlign: "center"
      }
    }
  ]
};

console.log('Canvas MCP Server Examples');
console.log('==========================');
console.log('');
console.log('Basic workflow:', JSON.stringify(diagramWorkflow, null, 2));
console.log('');
console.log('Chart example:', JSON.stringify(chartExample, null, 2));

export { examples, diagramWorkflow, chartExample };
