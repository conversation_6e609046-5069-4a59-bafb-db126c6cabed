import { createCanvas, Canvas, CanvasRenderingContext2D, loadImage } from 'canvas';
import sharp from 'sharp';
import {
  CanvasConfig,
  Rectangle,
  Circle,
  Line,
  Text,
  Path,
  ImageData,
  Filter,
  CanvasResult
} from './types.js';

export class CanvasManager {
  private canvas: Canvas;
  private ctx: CanvasRenderingContext2D;
  private config: CanvasConfig;

  constructor(config: CanvasConfig) {
    this.config = config;
    this.canvas = createCanvas(config.width, config.height);
    this.ctx = this.canvas.getContext('2d');
    this.clear();
  }

  /**
   * Clear the canvas with background color
   */
  clear(): void {
    this.ctx.fillStyle = this.config.backgroundColor;
    this.ctx.fillRect(0, 0, this.config.width, this.config.height);
  }

  /**
   * Draw a rectangle
   */
  drawRectangle(rect: Rectangle): void {
    if (rect.fillColor) {
      this.ctx.fillStyle = rect.fillColor;
      this.ctx.fillRect(rect.x, rect.y, rect.width, rect.height);
    }
    
    if (rect.strokeColor) {
      this.ctx.strokeStyle = rect.strokeColor;
      this.ctx.lineWidth = rect.strokeWidth;
      this.ctx.strokeRect(rect.x, rect.y, rect.width, rect.height);
    }
  }

  /**
   * Draw a circle
   */
  drawCircle(circle: Circle): void {
    this.ctx.beginPath();
    this.ctx.arc(circle.x, circle.y, circle.radius, 0, 2 * Math.PI);
    
    if (circle.fillColor) {
      this.ctx.fillStyle = circle.fillColor;
      this.ctx.fill();
    }
    
    if (circle.strokeColor) {
      this.ctx.strokeStyle = circle.strokeColor;
      this.ctx.lineWidth = circle.strokeWidth;
      this.ctx.stroke();
    }
  }

  /**
   * Draw a line
   */
  drawLine(line: Line): void {
    this.ctx.beginPath();
    this.ctx.moveTo(line.startX, line.startY);
    this.ctx.lineTo(line.endX, line.endY);
    this.ctx.strokeStyle = line.strokeColor;
    this.ctx.lineWidth = line.strokeWidth;
    this.ctx.lineCap = line.lineCap;
    this.ctx.stroke();
  }

  /**
   * Draw text
   */
  drawText(text: Text): void {
    this.ctx.font = `${text.fontSize}px ${text.fontFamily}`;
    this.ctx.fillStyle = text.fillColor;
    this.ctx.textAlign = text.textAlign;
    this.ctx.textBaseline = text.textBaseline;
    this.ctx.fillText(text.text, text.x, text.y);
  }

  /**
   * Draw a path
   */
  drawPath(path: Path): void {
    if (path.points.length < 2) return;

    this.ctx.beginPath();
    this.ctx.moveTo(path.points[0].x, path.points[0].y);
    
    for (let i = 1; i < path.points.length; i++) {
      this.ctx.lineTo(path.points[i].x, path.points[i].y);
    }
    
    if (path.closed) {
      this.ctx.closePath();
    }
    
    if (path.fillColor && path.closed) {
      this.ctx.fillStyle = path.fillColor;
      this.ctx.fill();
    }
    
    this.ctx.strokeStyle = path.strokeColor;
    this.ctx.lineWidth = path.strokeWidth;
    this.ctx.stroke();
  }

  /**
   * Draw an image
   */
  async drawImage(imageData: ImageData): Promise<void> {
    try {
      const img = await loadImage(imageData.src);
      const width = imageData.width || img.width;
      const height = imageData.height || img.height;
      
      this.ctx.globalAlpha = imageData.opacity;
      this.ctx.drawImage(img, imageData.x, imageData.y, width, height);
      this.ctx.globalAlpha = 1.0;
    } catch (error) {
      throw new Error(`Failed to load image: ${error}`);
    }
  }

  /**
   * Apply filters to the canvas
   */
  async applyFilter(filter: Filter): Promise<void> {
    const imageData = this.ctx.getImageData(0, 0, this.config.width, this.config.height);
    const buffer = Buffer.from(imageData.data);
    
    let sharpImage = sharp(buffer, {
      raw: {
        width: this.config.width,
        height: this.config.height,
        channels: 4
      }
    });

    switch (filter.type) {
      case 'blur':
        sharpImage = sharpImage.blur(filter.radius);
        break;
      case 'brightness':
        sharpImage = sharpImage.modulate({ brightness: filter.value });
        break;
      case 'contrast':
        sharpImage = sharpImage.linear(filter.value, 0);
        break;
      case 'saturate':
        sharpImage = sharpImage.modulate({ saturation: filter.value });
        break;
    }

    const processedBuffer = await sharpImage.raw().toBuffer();
    const processedImageData = new ImageData(
      new Uint8ClampedArray(processedBuffer),
      this.config.width,
      this.config.height
    );
    
    this.ctx.putImageData(processedImageData, 0, 0);
  }

  /**
   * Export canvas as base64 string
   */
  async export(): Promise<CanvasResult> {
    try {
      let buffer: Buffer;
      
      if (this.config.format === 'svg') {
        // For SVG, we need to convert to PNG first
        buffer = this.canvas.toBuffer('image/png');
      } else {
        buffer = this.canvas.toBuffer(`image/${this.config.format}` as any, {
          quality: this.config.quality
        });
      }

      const base64 = buffer.toString('base64');
      const dataUrl = `data:image/${this.config.format};base64,${base64}`;

      return {
        success: true,
        data: dataUrl,
        metadata: {
          width: this.config.width,
          height: this.config.height,
          format: this.config.format,
          size: buffer.length
        }
      };
    } catch (error) {
      return {
        success: false,
        message: `Export failed: ${error}`
      };
    }
  }

  /**
   * Get canvas dimensions
   */
  getDimensions(): { width: number; height: number } {
    return {
      width: this.config.width,
      height: this.config.height
    };
  }

  /**
   * Resize canvas
   */
  resize(width: number, height: number): void {
    const imageData = this.ctx.getImageData(0, 0, this.config.width, this.config.height);

    this.config.width = width;
    this.config.height = height;
    this.canvas.width = width;
    this.canvas.height = height;

    this.clear();
    this.ctx.putImageData(imageData, 0, 0);
  }

  /**
   * Save current state
   */
  save(): void {
    this.ctx.save();
  }

  /**
   * Restore previous state
   */
  restore(): void {
    this.ctx.restore();
  }

  /**
   * Set global composite operation
   */
  setCompositeOperation(operation: GlobalCompositeOperation): void {
    this.ctx.globalCompositeOperation = operation;
  }

  /**
   * Create gradient
   */
  createLinearGradient(x0: number, y0: number, x1: number, y1: number): CanvasGradient {
    return this.ctx.createLinearGradient(x0, y0, x1, y1);
  }

  /**
   * Create radial gradient
   */
  createRadialGradient(x0: number, y0: number, r0: number, x1: number, y1: number, r1: number): CanvasGradient {
    return this.ctx.createRadialGradient(x0, y0, r0, x1, y1, r1);
  }
}
