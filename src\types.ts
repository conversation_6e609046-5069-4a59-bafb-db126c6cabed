import { z } from 'zod';

// Canvas configuration schema
export const CanvasConfigSchema = z.object({
  width: z.number().min(1).max(4096).default(800),
  height: z.number().min(1).max(4096).default(600),
  backgroundColor: z.string().default('#ffffff'),
  format: z.enum(['png', 'jpeg', 'webp', 'svg']).default('png'),
  quality: z.number().min(0).max(1).default(0.9)
});

// Drawing operations schemas
export const PointSchema = z.object({
  x: z.number(),
  y: z.number()
});

export const ColorSchema = z.string().regex(/^#[0-9A-Fa-f]{6}$|^#[0-9A-Fa-f]{3}$|^rgb\(|^rgba\(|^hsl\(|^hsla\(/);

export const RectangleSchema = z.object({
  x: z.number(),
  y: z.number(),
  width: z.number().min(0),
  height: z.number().min(0),
  fillColor: ColorSchema.optional(),
  strokeColor: ColorSchema.optional(),
  strokeWidth: z.number().min(0).default(1)
});

export const CircleSchema = z.object({
  x: z.number(),
  y: z.number(),
  radius: z.number().min(0),
  fillColor: ColorSchema.optional(),
  strokeColor: ColorSchema.optional(),
  strokeWidth: z.number().min(0).default(1)
});

export const LineSchema = z.object({
  startX: z.number(),
  startY: z.number(),
  endX: z.number(),
  endY: z.number(),
  strokeColor: ColorSchema.default('#000000'),
  strokeWidth: z.number().min(0).default(1),
  lineCap: z.enum(['butt', 'round', 'square']).default('butt')
});

export const TextSchema = z.object({
  text: z.string(),
  x: z.number(),
  y: z.number(),
  fontSize: z.number().min(1).default(16),
  fontFamily: z.string().default('Arial'),
  fillColor: ColorSchema.default('#000000'),
  textAlign: z.enum(['left', 'center', 'right', 'start', 'end']).default('left'),
  textBaseline: z.enum(['top', 'hanging', 'middle', 'alphabetic', 'ideographic', 'bottom']).default('alphabetic')
});

export const PathSchema = z.object({
  points: z.array(PointSchema).min(2),
  strokeColor: ColorSchema.default('#000000'),
  strokeWidth: z.number().min(0).default(1),
  fillColor: ColorSchema.optional(),
  closed: z.boolean().default(false)
});

export const ImageSchema = z.object({
  src: z.string(), // base64 or URL
  x: z.number().default(0),
  y: z.number().default(0),
  width: z.number().optional(),
  height: z.number().optional(),
  opacity: z.number().min(0).max(1).default(1)
});

// Filter schemas
export const BlurFilterSchema = z.object({
  type: z.literal('blur'),
  radius: z.number().min(0).max(100).default(5)
});

export const BrightnessFilterSchema = z.object({
  type: z.literal('brightness'),
  value: z.number().min(0).max(2).default(1)
});

export const ContrastFilterSchema = z.object({
  type: z.literal('contrast'),
  value: z.number().min(0).max(2).default(1)
});

export const SaturationFilterSchema = z.object({
  type: z.literal('saturate'),
  value: z.number().min(0).max(2).default(1)
});

export const FilterSchema = z.union([
  BlurFilterSchema,
  BrightnessFilterSchema,
  ContrastFilterSchema,
  SaturationFilterSchema
]);

// Export types
export type CanvasConfig = z.infer<typeof CanvasConfigSchema>;
export type Point = z.infer<typeof PointSchema>;
export type Rectangle = z.infer<typeof RectangleSchema>;
export type Circle = z.infer<typeof CircleSchema>;
export type Line = z.infer<typeof LineSchema>;
export type Text = z.infer<typeof TextSchema>;
export type Path = z.infer<typeof PathSchema>;
export type ImageData = z.infer<typeof ImageSchema>;
export type Filter = z.infer<typeof FilterSchema>;

// Canvas operation result
export interface CanvasResult {
  success: boolean;
  message?: string;
  data?: string; // base64 encoded image
  metadata?: {
    width: number;
    height: number;
    format: string;
    size: number;
  };
}
