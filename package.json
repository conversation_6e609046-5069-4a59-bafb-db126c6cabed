{"name": "canvas-mcp-server", "version": "1.0.0", "description": "MCP server for Canvas drawing and graphics operations", "main": "dist/index.js", "type": "module", "scripts": {"build": "tsc", "dev": "tsx src/index.ts", "start": "node dist/index.js", "test": "jest", "lint": "eslint src/**/*.ts", "format": "prettier --write src/**/*.ts"}, "keywords": ["mcp", "canvas", "graphics", "drawing", "model-context-protocol"], "author": "Your Name", "license": "MIT", "dependencies": {"@modelcontextprotocol/sdk": "^0.4.0", "canvas": "^2.11.2", "sharp": "^0.33.0", "zod": "^3.22.4"}, "devDependencies": {"@types/node": "^20.10.0", "@typescript-eslint/eslint-plugin": "^6.13.0", "@typescript-eslint/parser": "^6.13.0", "eslint": "^8.54.0", "jest": "^29.7.0", "prettier": "^3.1.0", "tsx": "^4.6.0", "typescript": "^5.3.0"}, "engines": {"node": ">=18.0.0"}}